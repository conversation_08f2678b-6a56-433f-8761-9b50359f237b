rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Public read-only landing content example (optional):
    // match /public/{docId} {
    //   allow read: if true;
    //   allow write: if false;
    // }

    // Default: require authentication for all other collections.
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}

