import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDocs,
  getDoc,
  query,
  where,
  orderBy,
  Timestamp
} from 'firebase/firestore';
import { getFirebaseClients } from '@/app/Firebase/init';

export interface Certificate {
  id?: string;
  name: string;
  provider: string;
  description: string;
  backgroundUrl?: string;
  questionType: "Multiple Choice" | "Graded Multiple Choice" | "Text Input" | "Mix";
  domain: "Data Management" | "Enterprise Architecture" | "IT Governance";
  createdAt: Timestamp;
  updatedAt: Timestamp;
  createdBy: string;
  isActive: boolean;
  totalQuestions?: number;
  passingScore?: number;
}

export interface CertificateFormData {
  name: string;
  provider: string;
  description: string;
  backgroundUrl: string;
  questionType: "Multiple Choice" | "Graded Multiple Choice" | "Text Input" | "Mix";
  domain: "Data Management" | "Enterprise Architecture" | "IT Governance";
}

class CertificateService {
  private collectionName = 'certificates';

  /**
   * Create a new certificate
   */
  async createCertificate(certificateData: CertificateFormData, userId: string): Promise<string> {
    try {
      const { db } = getFirebaseClients();
      const now = Timestamp.now();
      const certificate: Omit<Certificate, 'id'> = {
        ...certificateData,
        createdAt: now,
        updatedAt: now,
        createdBy: userId,
        isActive: true,
        totalQuestions: 0,
        passingScore: 70
      };

      const docRef = await addDoc(collection(db, this.collectionName), certificate);
      return docRef.id;
    } catch (error) {
      console.error('Error creating certificate:', error);
      throw new Error('Failed to create certificate');
    }
  }

  /**
   * Get all certificates
   */
  async getAllCertificates(): Promise<Certificate[]> {
    try {
      const { db } = getFirebaseClients();
      const q = query(
        collection(db, this.collectionName),
        where('isActive', '==', true),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Certificate));
    } catch (error) {
      console.error('Error fetching certificates:', error);
      throw new Error('Failed to fetch certificates');
    }
  }

  /**
   * Get certificates by domain
   */
  async getCertificatesByDomain(domain: Certificate['domain']): Promise<Certificate[]> {
    try {
      const { db } = getFirebaseClients();
      const q = query(
        collection(db, this.collectionName),
        where('domain', '==', domain),
        where('isActive', '==', true),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Certificate));
    } catch (error) {
      console.error('Error fetching certificates by domain:', error);
      throw new Error('Failed to fetch certificates by domain');
    }
  }

  /**
   * Get a single certificate by ID
   */
  async getCertificateById(certificateId: string): Promise<Certificate | null> {
    try {
      const { db } = getFirebaseClients();
      const docRef = doc(db, this.collectionName, certificateId);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data()
        } as Certificate;
      }

      return null;
    } catch (error) {
      console.error('Error fetching certificate:', error);
      throw new Error('Failed to fetch certificate');
    }
  }

  /**
   * Update a certificate
   */
  async updateCertificate(certificateId: string, updates: Partial<CertificateFormData>): Promise<void> {
    try {
      const { db } = getFirebaseClients();
      const docRef = doc(db, this.collectionName, certificateId);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating certificate:', error);
      throw new Error('Failed to update certificate');
    }
  }

  /**
   * Delete a certificate (soft delete)
   */
  async deleteCertificate(certificateId: string): Promise<void> {
    try {
      const { db } = getFirebaseClients();
      const docRef = doc(db, this.collectionName, certificateId);
      await updateDoc(docRef, {
        isActive: false,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error deleting certificate:', error);
      throw new Error('Failed to delete certificate');
    }
  }

  /**
   * Get certificate statistics
   */
  async getCertificateStats(): Promise<{
    totalCertificates: number;
    certificatesByDomain: Record<Certificate['domain'], number>;
    totalQuestions: number;
  }> {
    try {
      const certificates = await this.getAllCertificates();
      
      const stats = {
        totalCertificates: certificates.length,
        certificatesByDomain: {
          "Data Management": 0,
          "Enterprise Architecture": 0,
          "IT Governance": 0
        } as Record<Certificate['domain'], number>,
        totalQuestions: 0
      };

      certificates.forEach(cert => {
        stats.certificatesByDomain[cert.domain]++;
        stats.totalQuestions += cert.totalQuestions || 0;
      });

      return stats;
    } catch (error) {
      console.error('Error fetching certificate stats:', error);
      throw new Error('Failed to fetch certificate statistics');
    }
  }
}

export const certificateService = new CertificateService();
