"use client";

import { useEffect } from "react";
import { useLocale, useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { DtcHero } from "@/components/ui/Hero";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Shield } from "lucide-react";
import { useUser } from "@/hooks/useUser";

export default function KnowledgeHubManagementPage() {
  const { user } = useUser();
  const router = useRouter();
  const locale = useLocale();
  const t = useTranslations("knowledgeHub.manage");

  useEffect(() => {
    if (user && user.role !== "Admin") {
      router.push(`/${locale}/dashboard`);
    }
  }, [user, router, locale]);

  if (!user) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-primary/10 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-primary/10 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (user.role !== "Admin") {
    return (
      <div className="space-y-6">
        <Alert className="border-destructive/20 bg-destructive/5">
          <Shield className="h-4 w-4" />
          <AlertDescription>
            Access denied. This page is only available to Admin users.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <DtcHero
        title={t("heroTitle")}
        subtitle={t("heroSubtitle")}
        image="hero2"
      />

      {/* Empty content area - no cards */}
      <div className="min-h-[240px]" />
    </div>
  );
}


